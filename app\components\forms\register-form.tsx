import { useFetcher } from "@remix-run/react";
import { useState } from "react";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";

export function RegisterForm() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [name, setName] = useState("");
  const fetcher = useFetcher();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    fetcher.submit(
      { email, password, name },
      {
        method: "POST",
        action: "/api/auth/register",
        encType: "application/json",
      }
    );
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="name">Name (optional)</Label>
        <Input id="name" type="text" value={name} onChange={(e) => setName(e.target.value)} />
      </div>
      <div>
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
        />
      </div>
      <div>
        <Label htmlFor="password">Password</Label>
        <Input
          id="password"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          minLength={6}
        />
      </div>
      <Button type="submit" disabled={fetcher.state === "submitting"}>
        {fetcher.state === "submitting" ? "Creating account..." : "Create account"}
      </Button>
      {fetcher.data &&
      typeof fetcher.data === "object" &&
      "error" in fetcher.data &&
      fetcher.data.error ? (
        <p className="text-red-500 text-sm">{String(fetcher.data.error)}</p>
      ) : null}
      {fetcher.data &&
      typeof fetcher.data === "object" &&
      "message" in fetcher.data &&
      fetcher.data.message ? (
        <p className="text-green-500 text-sm">{String(fetcher.data.message)}</p>
      ) : null}
    </form>
  );
}
