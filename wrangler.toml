#:schema node_modules/wrangler/config-schema.json
name = "remix-cloudflare-neon-starter"

main = "./server.ts"
workers_dev = true
# https://developers.cloudflare.com/workers/platform/compatibility-dates
compatibility_date = "2024-09-26"
# Enable Node.js compatibility for built-in modules like crypto, fs, etc.
compatibility_flags = ["nodejs_compat"]

[assets]
# https://developers.cloudflare.com/workers/static-assets/binding/
directory = "./build/client"

[build]
command = "pnpm run build"

# R2 Storage Binding
[[r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "remix-cf-neon-starter-r2"
preview_bucket_name = "remix-cf-neon-starter-r2-preview"

# Cloudflare AI Binding
[ai]
binding = "AI"

# Environment Variables for Authentication
[vars]
# These will be overridden by secrets in production
NODE_ENV = "development"

# Add your environment variables here:
# GOOGLE_CLIENT_ID = "your-google-client-id"
# NEON_DATABASE_URL = "your-neon-connection-string"
# SESSION_COOKIE_SECRET = "your-session-secret"
