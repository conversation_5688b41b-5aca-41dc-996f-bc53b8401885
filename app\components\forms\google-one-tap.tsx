import { useFetcher } from "@remix-run/react";
import { useEffect } from "react";

interface GoogleOneTapProps {
  clientId: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

declare global {
  interface Window {
    google?: any;
  }
}

export function GoogleOneTap({ clientId, onSuccess, onError }: GoogleOneTapProps) {
  const fetcher = useFetcher();

  useEffect(() => {
    const initializeGoogleOneTap = () => {
      if (window.google?.accounts?.id) {
        window.google.accounts.id.initialize({
          client_id: clientId,
          callback: (response: any) => {
            fetcher.submit(
              { credential: response.credential },
              {
                method: "POST",
                action: "/api/auth/google",
                encType: "application/json",
              }
            );
          },
          auto_select: true,
          cancel_on_tap_outside: false,
        });

        window.google.accounts.id.prompt();
      }
    };

    // Load Google One Tap script
    if (!window.google) {
      const script = document.createElement("script");
      script.src = "https://accounts.google.com/gsi/client";
      script.async = true;
      script.defer = true;
      script.onload = initializeGoogleOneTap;
      document.head.appendChild(script);
    } else {
      initializeGoogleOneTap();
    }
  }, [clientId, fetcher]);

  useEffect(() => {
    if (
      fetcher.data &&
      typeof fetcher.data === "object" &&
      "success" in fetcher.data &&
      fetcher.data.success
    ) {
      onSuccess?.();
    } else if (fetcher.data && typeof fetcher.data === "object" && "error" in fetcher.data) {
      onError?.(fetcher.data.error as string);
    }
  }, [fetcher.data, onSuccess, onError]);

  return null;
}

export function GoogleSignInButton({ clientId }: { clientId: string }) {
  const fetcher = useFetcher();

  useEffect(() => {
    const initializeButton = () => {
      if (window.google?.accounts?.id) {
        const buttonDiv = document.getElementById("google-signin-button");
        if (buttonDiv) {
          window.google.accounts.id.renderButton(buttonDiv, {
            theme: "outline",
            size: "large",
            type: "standard",
            text: "signin_with",
            shape: "rectangular",
            logo_alignment: "left",
          });
        }
      }
    };

    // Load Google One Tap script
    if (!window.google) {
      const script = document.createElement("script");
      script.src = "https://accounts.google.com/gsi/client";
      script.async = true;
      script.defer = true;
      script.onload = () => {
        window.google?.accounts?.id?.initialize({
          client_id: clientId,
          callback: (response: any) => {
            fetcher.submit(
              { credential: response.credential },
              {
                method: "POST",
                action: "/api/auth/google",
                encType: "application/json",
              }
            );
          },
        });
        initializeButton();
      };
      document.head.appendChild(script);
    } else {
      initializeButton();
    }
  }, [clientId, fetcher]);

  return <div id="google-signin-button" />;
}
