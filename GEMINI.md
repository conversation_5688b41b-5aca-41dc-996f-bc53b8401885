# Gemini Codebase Overview

This document provides a high-level overview of the codebase structure, organized by feature modules.

## Feature Modules

The application is divided into the following feature modules, located in the `app/features` directory:

- **admin**: Admin dashboard, user management, and other administrative functionalities.
- **ai-tools**: Core AI functionalities, including text generation and other AI-powered tools.
- **auth**: User authentication, including login, registration, and session management.
- **billing**: Manages billing, subscriptions, and payment processing.
- **blog**: The blog feature, including posts, categories, and content management.
- **chat**: Real-time chat functionality, including conversations and messages.
- **console**: The user console, where users can manage their account and services.
- **deployment**: Manages application deployment and related tasks.
- **email**: <PERSON>les email sending and templates.
- **feedback**: Manages user feedback and suggestions.
- **monitoring**: Application monitoring, including performance and error tracking.
- **notifications**: Manages user notifications.
- **onboarding**: New user onboarding and setup flows.
- **orders**: Manages user orders and purchase history.
- **payment**: Payment processing and related functionalities.
- **performance**: Performance monitoring and optimization tools.
- **security**: Security-related features, including middleware and access control.
- **storage**: Manages file storage, including image uploads and other assets.
- **user**: User profile management, including user settings and preferences.
