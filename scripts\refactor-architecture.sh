#!/bin/bash

# 三层架构重构脚本
# 用于批量移动文件和更新导入路径

echo "🚀 开始三层架构重构..."

# 创建必要的目录
echo "📁 创建目录结构..."
mkdir -p app/routes/{admin,ai-tools,billing,console,auth,payment}
mkdir -p app/components/{admin,ai-tools,billing,console,auth,payment,user}
mkdir -p app/types
mkdir -p app/features/{admin,ai-tools,billing,console,auth,payment,user}/services

# 移动所有feature routes到app/routes
echo "📋 移动routes文件..."

# Admin routes
for file in app/features/admin/routes/*.tsx; do
  if [ -f "$file" ]; then
    filename=$(basename "$file")
    if [ "$filename" = "_index.tsx" ]; then
      cp "$file" "app/routes/admin._index.tsx"
    else
      cp "$file" "app/routes/admin.${filename}"
    fi
    echo "移动: $file -> app/routes/admin.${filename}"
  fi
done

# AI Tools routes
for file in app/features/ai-tools/routes/*.tsx; do
  if [ -f "$file" ]; then
    filename=$(basename "$file")
    cp "$file" "app/routes/ai-tools.${filename}"
    echo "移动: $file -> app/routes/ai-tools.${filename}"
  fi
done

# Billing routes
for file in app/features/billing/routes/*.tsx; do
  if [ -f "$file" ]; then
    filename=$(basename "$file")
    cp "$file" "app/routes/billing.${filename}"
    echo "移动: $file -> app/routes/billing.${filename}"
  fi
done

# Console routes
for file in app/features/console/routes/*.tsx; do
  if [ -f "$file" ]; then
    filename=$(basename "$file")
    if [ "$filename" = "_index.tsx" ]; then
      cp "$file" "app/routes/console._index.tsx"
    else
      cp "$file" "app/routes/console.${filename}"
    fi
    echo "移动: $file -> app/routes/console.${filename}"
  fi
done

# Auth routes
for file in app/features/auth/routes/*.tsx; do
  if [ -f "$file" ]; then
    filename=$(basename "$file")
    cp "$file" "app/routes/auth.${filename}"
    echo "移动: $file -> app/routes/auth.${filename}"
  fi
done

# Payment routes
for file in app/features/payment/routes/*.tsx; do
  if [ -f "$file" ]; then
    filename=$(basename "$file")
    cp "$file" "app/routes/payment.${filename}"
    echo "移动: $file -> app/routes/payment.${filename}"
  fi
done

# 移动组件到app/components
echo "🎨 移动组件文件..."

# 移动所有feature组件
for feature in admin ai-tools billing console auth payment user; do
  if [ -d "app/features/$feature/components" ]; then
    cp -r "app/features/$feature/components" "app/components/$feature"
    echo "移动: app/features/$feature/components -> app/components/$feature"
  fi
done

echo "✅ 文件移动完成！"

# 创建feature index文件
echo "📦 创建feature导出文件..."

for feature in admin ai-tools billing console auth payment user; do
  if [ -d "app/features/$feature" ]; then
    cat > "app/features/$feature/index.ts" << EOF
/**
 * $feature Feature Services
 * Exports all $feature-related services
 */

// Export all services
export * from "./services";

// TODO: Add specific service exports as they are created
EOF
    echo "创建: app/features/$feature/index.ts"
  fi
done

echo "🔄 重构脚本执行完成！"
echo ""
echo "📋 下一步需要手动完成："
echo "1. 更新所有import路径"
echo "2. 修复组件导入"
echo "3. 测试所有功能"
echo "4. 更新类型定义"
echo ""
echo "请查看 ARCHITECTURE_REFACTOR.md 了解详细信息"
