/**
 * Chat Sidebar Component - Optimized for Homepage
 * Handles conversation list with support for guest users
 */

import { Link, useNavigate } from "@remix-run/react";
import { MessageSquare, Plus, Archive, LogIn, User } from "lucide-react";
import { memo, useCallback } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { cn } from "~/core/utils/utils";

export interface ChatConversation {
  id: string;
  title: string;
  model?: string;
  provider?: string;
  isArchived: boolean;
  lastMessageAt?: string;
  createdAt: string;
  updatedAt: string;
}

interface ChatSidebarProps {
  conversations: ChatConversation[];
  currentConversationId?: string;
  user?: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
    credits?: number;
  } | null;
  isLoading?: boolean;
  onNewChat: () => void;
  onSelectConversation: (id: string) => void;
  onArchiveConversation: (id: string) => void;
  className?: string;
}

const ChatSidebar = memo(function ChatSidebar({
  conversations,
  currentConversationId,
  user,
  isLoading = false,
  onNewChat,
  onSelectConversation,
  onArchiveConversation,
  className,
}: ChatSidebarProps) {
  const navigate = useNavigate();

  const handleConversationClick = useCallback(
    (conversationId: string) => {
      onSelectConversation(conversationId);
      navigate(`/?conversation=${conversationId}`);
    },
    [onSelectConversation, navigate]
  );

  const handleArchive = useCallback(
    (e: React.MouseEvent, conversationId: string) => {
      e.stopPropagation();
      onArchiveConversation(conversationId);
    },
    [onArchiveConversation]
  );

  const formatLastMessage = (lastMessageAt?: string) => {
    if (!lastMessageAt) return "";
    
    const date = new Date(lastMessageAt);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) return "Today";
    if (days === 1) return "Yesterday";
    if (days < 7) return `${days} days ago`;
    return date.toLocaleDateString();
  };

  return (
    <div className={cn("flex flex-col h-full bg-muted/30 border-r border-border", className)}>
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold">AI Chat</h2>
          <Button
            onClick={onNewChat}
            size="sm"
            className="flex items-center gap-2"
            disabled={isLoading}
          >
            <Plus className="h-4 w-4" />
            New
          </Button>
        </div>

        {/* User Status */}
        {user ? (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <User className="h-4 w-4" />
            <span className="truncate">{user.name}</span>
            {user.credits !== undefined && (
              <Badge variant="secondary" className="text-xs">
                {user.credits} credits
              </Badge>
            )}
          </div>
        ) : (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <LogIn className="h-4 w-4" />
            <span>Guest Mode</span>
            <Badge variant="outline" className="text-xs">
              Limited
            </Badge>
          </div>
        )}
      </div>

      {/* Conversations List */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="p-4 space-y-3">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-16 bg-muted/50 rounded-lg animate-pulse" />
            ))}
          </div>
        ) : conversations.length > 0 ? (
          <div className="p-2 space-y-1">
            {conversations.map((conversation) => (
              <div
                key={conversation.id}
                onClick={() => handleConversationClick(conversation.id)}
                className={cn(
                  "group relative p-3 rounded-lg cursor-pointer transition-all duration-200",
                  "hover:bg-muted/50 border border-transparent",
                  currentConversationId === conversation.id
                    ? "bg-primary/10 border-primary/20 text-primary"
                    : "hover:border-border"
                )}
              >
                <div className="flex items-start justify-between gap-2">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <MessageSquare className="h-4 w-4 flex-shrink-0" />
                      <h3 className="font-medium text-sm truncate">
                        {conversation.title}
                      </h3>
                    </div>
                    
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      {conversation.provider && (
                        <Badge variant="outline" className="text-xs px-1 py-0">
                          {conversation.provider}
                        </Badge>
                      )}
                      <span>{formatLastMessage(conversation.lastMessageAt)}</span>
                    </div>
                  </div>

                  {/* Archive button */}
                  <Button
                    onClick={(e) => handleArchive(e, conversation.id)}
                    variant="ghost"
                    size="sm"
                    className="opacity-0 group-hover:opacity-100 transition-opacity p-1 h-6 w-6"
                  >
                    <Archive className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="p-6 text-center text-muted-foreground">
            <MessageSquare className="h-8 w-8 mx-auto mb-3 opacity-50" />
            <p className="text-sm">No conversations yet</p>
            <p className="text-xs mt-1">Start a new chat to begin</p>
          </div>
        )}
      </div>

      {/* Footer */}
      {!user && (
        <div className="p-4 border-t border-border">
          <div className="text-center space-y-2">
            <p className="text-xs text-muted-foreground">
              Sign in to save conversations
            </p>
            <Button asChild variant="outline" size="sm" className="w-full">
              <Link to="/auth/login">Sign In</Link>
            </Button>
          </div>
        </div>
      )}
    </div>
  );
});

export default ChatSidebar;
export type { ChatConversation, ChatSidebarProps };
