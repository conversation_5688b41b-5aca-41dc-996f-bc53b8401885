import { useFetcher } from "@remix-run/react";
import { useState } from "react";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";

export function LoginForm() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const fetcher = useFetcher();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    fetcher.submit(
      { email, password },
      {
        method: "POST",
        action: "/api/auth/login",
        encType: "application/json",
      }
    );
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
        />
      </div>
      <div>
        <Label htmlFor="password">Password</Label>
        <Input
          id="password"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
        />
      </div>
      <Button type="submit" disabled={fetcher.state === "submitting"}>
        {fetcher.state === "submitting" ? "Signing in..." : "Sign in"}
      </Button>
      {fetcher.data &&
      typeof fetcher.data === "object" &&
      "error" in fetcher.data &&
      fetcher.data.error ? (
        <p className="text-red-500 text-sm">{String(fetcher.data.error)}</p>
      ) : null}
    </form>
  );
}
