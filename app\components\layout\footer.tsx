import { Link } from "@remix-run/react";
import { <PERSON><PERSON><PERSON>, Linkedin, Mail, Shield, Twitter } from "lucide-react";
import type { FooterLinkSection, SocialPlatform } from "~/config";
import { footerLinks, getAnalyticsProps, isExternalUrl, siteConfig } from "~/config";

export interface FooterProps {
  brand?: {
    title: string;
    description: string;
    tagline?: string;
  };
  links?: readonly FooterLinkSection[];
  social?: Partial<Record<SocialPlatform, string>>;
  copyright?: string;
}

// Default values now come from siteConfig

export default function Footer({
  brand = {
    title: siteConfig.name,
    description: siteConfig.description,
    tagline: siteConfig.tagline,
  },
  links = footerLinks,
  social = siteConfig.social,
  copyright = "© 2024 AI SaaS Starter. All rights reserved.",
}: FooterProps) {
  return (
    <footer className="relative border-t border-border/40 bg-gradient-to-b from-background to-muted/20">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-pattern opacity-[0.02]" aria-hidden="true" />
      <div className="container mx-auto px-4 py-16 relative">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <Link to="/" className="flex items-center gap-3 mb-6 group">
              <div className="h-10 w-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center transition-all group-hover:scale-110 group-hover:shadow-lg group-hover:shadow-blue-500/25">
                <span className="text-white font-bold text-lg">AI</span>
              </div>
              <div className="flex flex-col">
                <span className="text-xl font-bold text-foreground">{brand.title}</span>
                <span className="text-xs text-muted-foreground -mt-1">Next-Gen SaaS Platform</span>
              </div>
            </Link>
            <p className="text-muted-foreground text-base mb-8 max-w-sm leading-relaxed">
              {brand.description}
            </p>

            {/* Social Links */}
            <div className="flex gap-3">
              {social.github && (
                <a
                  href={social.github}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group p-2 bg-muted/50 hover:bg-gray-700 rounded-lg transition-all duration-300 hover:scale-110"
                  aria-label="GitHub"
                  data-analytics="social-link"
                  data-platform="github"
                >
                  <Github className="h-5 w-5 text-muted-foreground group-hover:text-white transition-colors" />
                </a>
              )}
              {social.twitter && (
                <a
                  href={social.twitter}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group p-2 bg-muted/50 hover:bg-blue-400 rounded-lg transition-all duration-300 hover:scale-110"
                  aria-label="Twitter"
                  data-analytics="social-link"
                  data-platform="twitter"
                >
                  <Twitter className="h-5 w-5 text-muted-foreground group-hover:text-white transition-colors" />
                </a>
              )}
              {social.linkedin && (
                <a
                  href={social.linkedin}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group p-2 bg-muted/50 hover:bg-blue-600 rounded-lg transition-all duration-300 hover:scale-110"
                  aria-label="LinkedIn"
                  data-analytics="social-link"
                  data-platform="linkedin"
                >
                  <Linkedin className="h-5 w-5 text-muted-foreground group-hover:text-white transition-colors" />
                </a>
              )}
              {social.email && (
                <a
                  href={social.email}
                  className="group p-2 bg-muted/50 hover:bg-purple-500 rounded-lg transition-all duration-300 hover:scale-110"
                  aria-label="Email"
                  data-analytics="social-link"
                  data-platform="email"
                >
                  <Mail className="h-5 w-5 text-muted-foreground group-hover:text-white transition-colors" />
                </a>
              )}
            </div>
          </div>

          {/* Links Sections */}
          {links.map((section, i) => (
            <div key={`${section.title}-${i}`}>
              <h3 className="font-semibold mb-4">{section.title}</h3>
              <ul className="space-y-3">
                {section.items.map(
                  (item: { title: string; href: string; internal?: boolean }, ii: number) => (
                    <li key={`${item.title}-${ii}`}>
                      {!item.internal || isExternalUrl(item.href) ? (
                        <a
                          href={item.href}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                          {...getAnalyticsProps(section.title, item.title)}
                        >
                          {item.title}
                        </a>
                      ) : (
                        <Link
                          to={item.href}
                          className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                          {...getAnalyticsProps(section.title, item.title)}
                        >
                          {item.title}
                        </Link>
                      )}
                    </li>
                  )
                )}
              </ul>
            </div>
          ))}
        </div>

        {/* Bottom Section - 只显示版权和SSL标识，避免重复链接 */}
        <div className="border-t mt-12 pt-8 flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="flex flex-col md:flex-row items-center gap-4">
            <p className="text-sm text-muted-foreground">{copyright}</p>
            {/* Trust indicators */}
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4 text-green-600" />
              <span className="text-xs text-muted-foreground">SSL Secured</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
