import {
  BarChart3,
  BookOpen,
  Bot,
  Code,
  FileText,
  Home,
  MessageSquare,
  Plus,
  Settings,
  Sparkles,
  Users,
  Zap,
} from "lucide-react";
import { createElement, type ReactNode } from "react";
import type { SidebarItem } from "~/components/layout/sidebar";

// Helper function to create icon elements
// Note: Icon sizing should be handled by the component, not config
const createIcon = (IconComponent: React.ElementType): ReactNode =>
  createElement(IconComponent, { className: "h-4 w-4" });

// Default sidebar configuration for the main application
export const defaultSidebarConfig: SidebarItem[] = [
  {
    title: "Home",
    url: "/",
    icon: createIcon(Home),
    description: "Back to homepage",
  },
  {
    title: "AI Tools",
    url: "/dev/ai-tools",
    icon: createIcon(Bot),
    description: "AI-powered development tools",
    children: [
      {
        title: "Text Generation",
        url: "/dev/ai-tools/text",
        icon: createIcon(FileText),
        description: "Generate text with AI",
      },
      {
        title: "Chat Interface",
        url: "/dev/ai-tools/chat",
        icon: createIcon(MessageSquare),
        description: "Interactive AI chat",
      },
      {
        title: "Code Assistant",
        url: "/dev/ai-tools/code",
        icon: createIcon(Code),
        description: "AI-powered coding help",
      },
      {
        title: "Image Generation",
        url: "/dev/ai-tools/image",
        icon: createIcon(Sparkles),
        description: "Create images with AI",
      },
    ],
  },
  {
    title: "Components",
    url: "/dev/components",
    icon: createIcon(Sparkles),
    description: "UI component showcase",
  },
  {
    title: "Performance",
    url: "/dev/performance",
    icon: createIcon(BarChart3),
    description: "Performance monitoring",
  },
  {
    title: "Documentation",
    url: "/docs",
    icon: createIcon(BookOpen),
    description: "API and usage documentation",
    children: [
      {
        title: "Getting Started",
        url: "/docs/getting-started",
        icon: createIcon(Zap),
        description: "Quick start guide",
      },
      {
        title: "API Reference",
        url: "/docs/api",
        icon: createIcon(Code),
        description: "Complete API documentation",
      },
      {
        title: "Examples",
        url: "/docs/examples",
        icon: createIcon(FileText),
        description: "Code examples and tutorials",
      },
    ],
  },
  {
    title: "Community",
    url: "/community",
    icon: createIcon(Users),
    badge: "New",
    description: "Join our developer community",
  },
];

// Chat-focused sidebar configuration
export const chatSidebarConfig: SidebarItem[] = [
  {
    title: "New Chat",
    url: "/",
    icon: createIcon(Plus),
    description: "Start a new conversation",
  },
  {
    title: "Landing Page",
    url: "/landingpage",
    icon: createIcon(Sparkles),
    description: "Explore our features",
  },
  {
    title: "AI Tools",
    url: "/dev/ai-tools",
    icon: createIcon(Bot),
    description: "AI-powered tools",
  },
  {
    title: "Documentation",
    url: "/docs",
    icon: createIcon(BookOpen),
    description: "Learn how to use our platform",
  },
];

// Developer-focused sidebar configuration
export const developerSidebarConfig: SidebarItem[] = [
  {
    title: "Dashboard",
    url: "/dev",
    icon: createIcon(BarChart3),
    description: "Development dashboard",
  },
  {
    title: "AI Tools",
    url: "/dev/ai-tools",
    icon: createIcon(Bot),
    description: "AI development tools",
    children: [
      {
        title: "Text Generation",
        url: "/dev/ai-tools/text",
        icon: createIcon(FileText),
      },
      {
        title: "Chat Interface",
        url: "/dev/ai-tools/chat",
        icon: createIcon(MessageSquare),
      },
      {
        title: "Code Assistant",
        url: "/dev/ai-tools/code",
        icon: createIcon(Code),
      },
    ],
  },
  {
    title: "Components",
    url: "/dev/components",
    icon: createIcon(Sparkles),
    description: "UI components",
  },
  {
    title: "Database",
    url: "/dev/database",
    icon: createIcon(Settings),
    description: "Database management",
  },
  {
    title: "Performance",
    url: "/dev/performance",
    icon: createIcon(BarChart3),
    description: "Performance monitoring",
  },
];

// Sidebar configuration for different contexts
export const sidebarConfigs = {
  default: defaultSidebarConfig,
  chat: chatSidebarConfig,
  developer: developerSidebarConfig,
} as const;

export type SidebarConfigType = keyof typeof sidebarConfigs;

// Helper function to get sidebar configuration
export function getSidebarConfig(type: SidebarConfigType = "default"): SidebarItem[] {
  return sidebarConfigs[type];
}

// Note: UI styles moved to the Sidebar component itself
// Config files should only contain data and business logic
