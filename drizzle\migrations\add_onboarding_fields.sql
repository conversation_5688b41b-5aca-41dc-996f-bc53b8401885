-- Add onboarding tracking fields to users table
-- Migration: Add onboarding fields
-- Date: 2024-12-19

-- Add onboarding completion tracking fields
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS has_seen_onboarding BOOLEAN DEFAULT FALSE NOT NULL,
ADD COLUMN IF NOT EXISTS onboarding_completed_at TIMESTAMPTZ;

-- Update existing users to have seen onboarding (optional)
-- Uncomment the line below if you want existing users to skip onboarding
-- UPDATE users SET has_seen_onboarding = TRUE WHERE created_at < NOW();

-- Add index for onboarding queries
CREATE INDEX IF NOT EXISTS idx_users_onboarding ON users(has_seen_onboarding, created_at);

-- Add comment for documentation
COMMENT ON COLUMN users.has_seen_onboarding IS 'Whether user has completed the onboarding flow';
COMMENT ON COLUMN users.onboarding_completed_at IS 'Timestamp when user completed onboarding';
