import CardGrid from "./card-grid";
import ContentSection from "./content-section";

interface FeatureItem {
  title: string;
  description: string;
  icon: string;
  image?: string;
}

interface FeatureShowcaseProps {
  title: string;
  description: string;
  items: FeatureItem[];
}

export default function FeatureShowcase({ title, description, items }: FeatureShowcaseProps) {
  return (
    <ContentSection
      title={title}
      description={description}
      background="features"
      decorations={true}
      padding="md"
      headerSpacing="md"
    >
      <CardGrid items={items} columns={3} variant="feature" animationDelay={0.1} />
    </ContentSection>
  );
}
