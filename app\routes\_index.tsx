/**
 * Homepage Route - AI Chat Interface
 * Minimal version for basic functionality
 */

import type { MetaFunction } from "@remix-run/cloudflare";
import { useState } from "react";
import { cn } from "~/core/utils/utils";

// SEO Meta
export const meta: MetaFunction = () => {
  return [
    { title: "AI Chat Interface - Start Your AI-Powered Conversations" },
    { 
      name: "description", 
      content: "Experience the power of AI with our advanced chat interface. Chat with multiple AI models, generate content, and explore AI capabilities." 
    },
    {
      name: "keywords",
      content: "AI chat, artificial intelligence, OpenAI, GPT, Claude, chat interface, AI assistant"
    }
  ];
};

export default function Homepage() {
  const [messages, setMessages] = useState<Array<{id: string, content: string, role: 'user' | 'assistant'}>>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;
    
    const userMessage = {
      id: Date.now().toString(),
      content: inputValue,
      role: 'user' as const
    };
    
    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);
    
    // 模拟AI响应
    setTimeout(() => {
      const aiMessage = {
        id: (Date.now() + 1).toString(),
        content: `这是对 "${userMessage.content}" 的AI回复。这是一个简单的演示界面。`,
        role: 'assistant' as const
      };
      setMessages(prev => [...prev, aiMessage]);
      setIsLoading(false);
    }, 1000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-4 py-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold text-gray-900">AI Chat Interface</h1>
          <p className="text-gray-600 mt-1">体验AI对话的强大功能</p>
        </div>
      </header>

      {/* Chat Container */}
      <div className="flex-1 max-w-4xl mx-auto w-full px-4 py-6">
        {/* Messages */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-4 min-h-[500px] flex flex-col">
          <div className="flex-1 p-6 overflow-y-auto">
            {messages.length === 0 ? (
              <div className="text-center text-gray-500 mt-20">
                <h2 className="text-xl font-semibold mb-2">开始您的AI对话</h2>
                <p>输入消息开始与AI助手对话</p>
              </div>
            ) : (
              <div className="space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={cn(
                      "flex",
                      message.role === 'user' ? "justify-end" : "justify-start"
                    )}
                  >
                    <div
                      className={cn(
                        "max-w-[80%] rounded-lg px-4 py-2",
                        message.role === 'user'
                          ? "bg-blue-500 text-white"
                          : "bg-gray-100 text-gray-900"
                      )}
                    >
                      <p className="whitespace-pre-wrap">{message.content}</p>
                    </div>
                  </div>
                ))}
                {isLoading && (
                  <div className="flex justify-start">
                    <div className="bg-gray-100 rounded-lg px-4 py-2">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Input Area */}
          <div className="border-t border-gray-200 p-4">
            <div className="flex space-x-3">
              <textarea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="输入您的消息..."
                className="flex-1 resize-none border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={2}
                disabled={isLoading}
              />
              <button
                onClick={handleSendMessage}
                disabled={isLoading || !inputValue.trim()}
                className={cn(
                  "px-6 py-2 rounded-lg font-medium transition-colors",
                  "bg-blue-500 text-white hover:bg-blue-600",
                  "disabled:bg-gray-300 disabled:cursor-not-allowed"
                )}
              >
                发送
              </button>
            </div>
          </div>
        </div>

        {/* Info Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">关于此演示</h2>
          <p className="text-gray-600 mb-4">
            这是一个基于 Remix + Cloudflare + TypeScript + Tailwind CSS 构建的最小AI聊天界面演示。
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">技术栈</h3>
              <ul className="text-gray-600 space-y-1">
                <li>• Remix Framework</li>
                <li>• Cloudflare Workers</li>
                <li>• TypeScript</li>
                <li>• Tailwind CSS</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">功能特性</h3>
              <ul className="text-gray-600 space-y-1">
                <li>• 实时聊天界面</li>
                <li>• 响应式设计</li>
                <li>• 简洁现代UI</li>
                <li>• 快速部署</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}