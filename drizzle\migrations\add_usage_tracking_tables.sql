-- Add usage tracking tables for API monitoring and analytics
-- Migration: Add usage tracking tables
-- Date: 2024-12-19

-- API Usage Tracking table - Track all API calls and usage metrics
CREATE TABLE IF NOT EXISTS api_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_uuid UUID NOT NULL REFERENCES users(uuid) ON DELETE CASCADE,
    endpoint VARCHAR(100) NOT NULL,
    method VARCHAR(10) NOT NULL,
    provider VARCHAR(50),
    model VARCHAR(100),
    request_size INTEGER,
    response_size INTEGER,
    tokens_used INTEGER,
    credits_used INTEGER,
    duration INTEGER NOT NULL,
    status VARCHAR(20) NOT NULL,
    error_code VARCHAR(50),
    error_message TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Usage Statistics table - Aggregated usage data for reporting and analytics
CREATE TABLE IF NOT EXISTS usage_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_uuid UUID NOT NULL REFERENCES users(uuid) ON DELETE CASCADE,
    period VARCHAR(20) NOT NULL,
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,
    total_requests INTEGER DEFAULT 0 NOT NULL,
    successful_requests INTEGER DEFAULT 0 NOT NULL,
    failed_requests INTEGER DEFAULT 0 NOT NULL,
    total_tokens INTEGER DEFAULT 0 NOT NULL,
    total_credits INTEGER DEFAULT 0 NOT NULL,
    total_cost DECIMAL(10,4) DEFAULT 0 NOT NULL,
    avg_response_time INTEGER,
    top_provider VARCHAR(50),
    top_model VARCHAR(100),
    top_endpoint VARCHAR(100),
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Rate Limiting table - Track rate limits and quotas
CREATE TABLE IF NOT EXISTS rate_limits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_uuid UUID NOT NULL REFERENCES users(uuid) ON DELETE CASCADE,
    endpoint VARCHAR(100) NOT NULL,
    window_start TIMESTAMPTZ NOT NULL,
    window_end TIMESTAMPTZ NOT NULL,
    request_count INTEGER DEFAULT 0 NOT NULL,
    token_count INTEGER DEFAULT 0 NOT NULL,
    credit_count INTEGER DEFAULT 0 NOT NULL,
    is_blocked BOOLEAN DEFAULT FALSE NOT NULL,
    blocked_until TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Indexes for api_usage table
CREATE INDEX IF NOT EXISTS idx_api_usage_user_uuid ON api_usage(user_uuid);
CREATE INDEX IF NOT EXISTS idx_api_usage_endpoint ON api_usage(endpoint);
CREATE INDEX IF NOT EXISTS idx_api_usage_provider ON api_usage(provider);
CREATE INDEX IF NOT EXISTS idx_api_usage_status ON api_usage(status);
CREATE INDEX IF NOT EXISTS idx_api_usage_created_at ON api_usage(created_at);
CREATE INDEX IF NOT EXISTS idx_api_usage_user_endpoint ON api_usage(user_uuid, endpoint);
CREATE INDEX IF NOT EXISTS idx_api_usage_user_provider ON api_usage(user_uuid, provider);

-- Indexes for usage_stats table
CREATE INDEX IF NOT EXISTS idx_usage_stats_user_uuid ON usage_stats(user_uuid);
CREATE INDEX IF NOT EXISTS idx_usage_stats_period ON usage_stats(period);
CREATE INDEX IF NOT EXISTS idx_usage_stats_period_start ON usage_stats(period_start);
CREATE INDEX IF NOT EXISTS idx_usage_stats_user_period ON usage_stats(user_uuid, period, period_start);

-- Indexes for rate_limits table
CREATE INDEX IF NOT EXISTS idx_rate_limits_user_uuid ON rate_limits(user_uuid);
CREATE INDEX IF NOT EXISTS idx_rate_limits_endpoint ON rate_limits(endpoint);
CREATE INDEX IF NOT EXISTS idx_rate_limits_window_start ON rate_limits(window_start);
CREATE INDEX IF NOT EXISTS idx_rate_limits_user_endpoint_window ON rate_limits(user_uuid, endpoint, window_start);

-- Comments for documentation
COMMENT ON TABLE api_usage IS 'Tracks all API calls with detailed metrics and metadata';
COMMENT ON TABLE usage_stats IS 'Aggregated usage statistics for reporting and analytics';
COMMENT ON TABLE rate_limits IS 'Rate limiting data for API endpoints';

COMMENT ON COLUMN api_usage.endpoint IS 'API endpoint that was called';
COMMENT ON COLUMN api_usage.method IS 'HTTP method (GET, POST, etc.)';
COMMENT ON COLUMN api_usage.provider IS 'AI provider used (if applicable)';
COMMENT ON COLUMN api_usage.model IS 'AI model used (if applicable)';
COMMENT ON COLUMN api_usage.tokens_used IS 'Number of tokens consumed';
COMMENT ON COLUMN api_usage.credits_used IS 'Number of credits deducted';
COMMENT ON COLUMN api_usage.duration IS 'Request duration in milliseconds';
COMMENT ON COLUMN api_usage.status IS 'Request status: success, error, timeout';

COMMENT ON COLUMN usage_stats.period IS 'Aggregation period: daily, weekly, monthly';
COMMENT ON COLUMN usage_stats.period_start IS 'Start of the aggregation period';
COMMENT ON COLUMN usage_stats.period_end IS 'End of the aggregation period';
COMMENT ON COLUMN usage_stats.total_cost IS 'Estimated cost in USD';

COMMENT ON COLUMN rate_limits.window_start IS 'Start of the rate limit window';
COMMENT ON COLUMN rate_limits.window_end IS 'End of the rate limit window';
COMMENT ON COLUMN rate_limits.request_count IS 'Number of requests in the window';
COMMENT ON COLUMN rate_limits.is_blocked IS 'Whether the user is currently blocked';

-- Create a function to automatically update updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_usage_stats_updated_at 
    BEFORE UPDATE ON usage_stats 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rate_limits_updated_at 
    BEFORE UPDATE ON rate_limits 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create a view for quick usage overview
CREATE OR REPLACE VIEW user_usage_overview AS
SELECT 
    u.uuid as user_uuid,
    u.name as user_name,
    u.email as user_email,
    COUNT(au.id) as total_requests,
    COUNT(CASE WHEN au.status = 'success' THEN 1 END) as successful_requests,
    COUNT(CASE WHEN au.status != 'success' THEN 1 END) as failed_requests,
    COALESCE(SUM(au.tokens_used), 0) as total_tokens,
    COALESCE(SUM(au.credits_used), 0) as total_credits,
    COALESCE(AVG(au.duration), 0) as avg_response_time,
    MAX(au.created_at) as last_request_at
FROM users u
LEFT JOIN api_usage au ON u.uuid = au.user_uuid
WHERE au.created_at >= NOW() - INTERVAL '30 days' OR au.created_at IS NULL
GROUP BY u.uuid, u.name, u.email
ORDER BY total_requests DESC;

COMMENT ON VIEW user_usage_overview IS 'Quick overview of user usage statistics for the last 30 days';
