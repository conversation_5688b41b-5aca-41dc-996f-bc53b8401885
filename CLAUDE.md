# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

**Package Manager**: This project uses **pnpm** as the package manager.

**Primary development workflow:**
- `pnpm run dev` - Start Remix dev server
- `pnpm run build` - Build for production
- `pnpm run start` - Start Wrangler dev server (after build)
- `pnpm run deploy` - Deploy to Cloudflare Workers

**Code quality:**
- `pnpm run check` - Run all Biome checks (lint + format)
- `pnpm run check:fix` - Fix all auto-fixable issues
- `pnpm run lint` / `pnpm run lint:fix` - Biome linting
- `pnpm run format` / `pnpm run format:fix` - Biome formatting
- `pnpm run typecheck` - TypeScript type checking
- `pnpm run test` - Run Vitest tests

**Database (Drizzle + Neon):**
- `pnpm run db:generate` - Generate migration files
- `pnpm run db:push` - Push schema changes (development)
- `pnpm run db:migrate` - Run migrations
- `pnpm run db:studio` - Open Drizzle Studio GUI
- `pnpm run typegen` - Generate Cloudflare bindings types

**HTTPS Development:**
- `pnpm run setup:https` - Set up HTTPS certificates for local development
- `pnpm run dev:https` - Start dev server with HTTPS (required for Google One Tap)
- `pnpm run dev:http` - Start dev server with HTTP (default)

**Environment setup:**
- Copy `.dev.vars.example` to `.dev.vars` for local environment variables
- DATABASE_URL required for Neon PostgreSQL connection
- API keys (OpenAI, Stripe, etc.) go in `.dev.vars` for local, `wrangler.toml` vars section for production

## Architecture Overview

**Tech Stack:**
- **Runtime**: Remix on Cloudflare Workers
- **Database**: Neon PostgreSQL with Drizzle ORM
- **Storage**: S3-compatible (Cloudflare R2 recommended)
- **State**: Zustand stores with TypeScript
- **Styling**: Tailwind CSS + Radix UI components
- **Analytics**: Google Analytics 4 integration
- **AI**: Multi-provider support (OpenAI, DeepSeek, OpenRouter, etc.)
- **Payments**: Stripe integration
- **i18n**: i18next with 6 languages (en, zh, es, fr, de, ja)

**Key Patterns:**

1. **Database**: Use `createDb(databaseUrl)` factory pattern with modular Drizzle schemas:
   - Main schema file: `app/lib/db/schema.ts` (re-exports all schemas)
   - Domain-specific schemas in `app/lib/db/schemas/` directory:
     - `shared.ts` - Shared enums and types
     - `users.ts` - User, auth, roles, sessions
     - `billing.ts` - Payments, subscriptions, orders
     - `chat.ts` - Conversations, messages, AI content
     - `content.ts` - Posts, feedback, content management
     - `monitoring.ts` - API usage, analytics, rate limiting

2. **State Management**: Zustand stores with selectors and actions:
   - `useUserStore`/`useAuthStore` - Authentication/user data (unified user management)
   - `useUIStore` - Theme, language, notifications, sidebar state
   - `useCartStore` - Shopping cart functionality
   - `useAppStore` - Global app state, initialization, online status
   - Import pattern: `import { useUser, useUserActions } from '~/stores'`

3. **i18n**: Language detection via cookies/URL params, resources in `app/lib/i18n/locales/`

4. **Components**: 
   - UI components in `app/components/ui/` (shadcn-style)
   - Layout components in `app/components/layout/`
   - Page blocks in `app/components/blocks/`

5. **Routes**: 
   - API routes prefixed with `api.`
   - Page routes follow Remix file-based routing
   - Landing page uses configurable blocks from `app/lib/landing-config.ts`

6. **Styling**: 
   - Theme system with CSS custom properties
   - Dark/light mode via Zustand store
   - Responsive design patterns

**Environment Variables:**
- Local: `.dev.vars` file
- Production: `wrangler.toml` vars section
- Required: DATABASE_URL, STRIPE keys, GA_TRACKING_ID
- Optional: AI provider keys, R2 storage config

**Testing:**
- Vitest for unit tests
- Test database connection via `/test-db` route
- Performance testing via `/performance` route

**Authentication:**
- Google OAuth with server-side token exchange
- Google One Tap integration (requires HTTPS in development)
- JWT-based session management
- Protected routes via `protected-route.tsx` component

**Development Routes:**
- `/dev/center` - Development dashboard with all dev tools
- `/dev/components` - Component showcase and testing
- `/dev/performance` - Performance monitoring dashboard
- `/dev/ai-tools` - AI provider testing and configuration
- `/test-*` routes - Various system testing endpoints